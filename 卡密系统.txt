// ==UserScript==
// @name         橙光无限花
// @version      *******
// @namespace    Janicerser
// @description  发现倒卖，会禁止脚本使用！
// <AUTHOR>
// @icon         https://m.riyugo.cn/i/2025/07/17/s934a4.jpg
// @grant        none
// @match        https://*.66rpg.com/h5/*

// ==/UserScript==

(function () {

  'use strict';

  const m = document.createElement("button");
  m.textContent = "janiceser";
  m.style.backgroundColor = "rgba(128, 128, 128, 0.5)";
  m.style.background = "#FFB6C1";
  m.style.color = "white";
  m.style.border = "2px solid #DB7093";
  m.style.zIndex = "9999";
  m.style.borderRadius = "30px";
  m.style.padding = "8px 10px";
  m.style.position = "fixed";
  m.style.top = "20px";
  m.style.left = "20px";
  m.style.select = "none";
  m.style.cursor = "pointer";
  document.body.appendChild(m);
  const n = document.createElement("div");
  n.style.position = "fixed";
  n.style.top = "55px";
  n.style.left = "20px";
  n.style.backgroundColor = "rgba(255, 255, 255, 0)";
  n.style.border = "#FFB6C1";
  n.style.display = "none";
  n.style.zIndex = "9998";
  document.body.appendChild(n);
  const a = (b, a) => {
    const c = document.createElement("div");
    c.textContent = b;
    c.style.padding = "11px 16px";
    c.style.cursor = "pointer";
    c.style.border = "2px solid #DB7093";
    c.style.margin = "3px";
    c.style.backgroundColor = "rgba(128, 128, 128, 0.5)";
    c.style.background = "#FFB6C1";
    c.style.color = "white";
    c.style.borderRadius = "20px";
    c.style.select = "none";
    c.onclick = a;
    n.appendChild(c);
    return c;
  };
  m.onclick = () => {
    n.style.display = n.style.display === "none" ? "block" : "none";
  };

  // \u901a\u7528\u5168\u5c4f\u5207\u6362\u51fd\u6570
  const toggleFullScreen = () => {
    // \u68c0\u67e5\u662f\u5426\u5df2\u5168\u5c4f
    const isFullscreen = !!document.fullscreenElement ||
                         !!document.webkitFullscreenElement ||
                         !!document.mozFullScreenElement ||
                         !!document.msFullscreenElement;

    if (!isFullscreen) {
      // \u8fdb\u5165\u5168\u5c4f
      const docEl = document.documentElement;
      if (docEl.requestFullscreen) {
        docEl.requestFullscreen().catch(err => {
          console.error(`\u8fdb\u5165\u5168\u5c4f\u5931\u8d25: ${err.message}`);
        });
      } else if (docEl.webkitRequestFullscreen) { // Chrome, Safari
        docEl.webkitRequestFullscreen();
      } else if (docEl.mozRequestFullScreen) { // Firefox
        docEl.mozRequestFullScreen();
      } else if (docEl.msRequestFullscreen) { // IE/Edge
        docEl.msRequestFullscreen();
      }
    } else {
      // \u9000\u51fa\u5168\u5c4f
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
      } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen();
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen();
      }
    }
  };

  a("\u5546\u57ce\u5f00\u5173", function () {
    q = !q;
    this.innerText = q ? "\u5546\u57ce\u5f00\u542f" : "\u5546\u57ce\u5173\u95ed";
    if (q) {
      console.log("\u62e6\u622a\u5668\u5df2\u5f00\u542f");
    } else {
      console.log("\u62e6\u622a\u5668\u5df2\u5173\u95ed");
    }
  });

  // \u6dfb\u52a0\u5168\u5c4f\u6309\u94ae
  a("\u5207\u6362\u5168\u5c4f", toggleFullScreen);

  a("\u4fee\u6539\u7d2f\u5145", () => {
    const inputAmount = prompt("\u8bf7\u586b\u5199\u9700\u8981\u7684\u9c9c\u82b1\u6570\u91cf\u5927\u5c0f");
       if (inputAmount) {
           var userData = getUserData();
               ['totalFlower', 'freshFlower', 'wildFlower', 'tempFlower', 'realFlower', 'haveFlower'].forEach(function(flowerType) {
                   userData[flowerType] = inputAmount;
               });
           \u7d2f\u5145 = \u9c9c\u82b1\u6570\u91cf("\u586b\u5199\u9c9c\u82b1\u6570\u503c")
       }
   }
 );
  a("\u9000\u51fa\u83dc\u5355", () => {
    n.style.display = "none";
  });
  a("\u5c4f\u853d\u6309\u94ae", () => {
    m.style.display = "none";
  });
  function o() {
    const f = new Date();
    const a = f.getFullYear().toString();
    const b = p(f.getMonth() + 1);
    const c = p(f.getDate());
    const d = p(f.getHours());
    const e = p(f.getMinutes());
    const g = p(f.getSeconds());
    const h = p(f.getMilliseconds(), 4);
    return "" + a + b + c + d + e + g + h;
  }
  function p(d, a = 2) {
    let b = d.toString();
    while (b.length < a) {
      b = "0" + b;
    }
    return b;
  }
  let q = false;
  const c = [];
  c.push({
    match: b => b.includes("/createBuyOrder"),
    modify: (e, a) => {
      const b = new URLSearchParams(a.split("?")[1]);
      const c = b.get("goods_id");
      const d = b.get("buy_num");
      const f = o();
      const g = {
        goods_id: c,
        order_id: "${orderId}",
        buy_num: d
      };
      const h = {
        status: 1,
        msg: "successful",
        data: g
      };
      return JSON.stringify(h);
    }
  });

    // \u62e6\u622aXMLHttpRequest\u8bf7\u6c42
    const originalXhrOpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
        // \u5904\u7406URL\uff0c\u5220\u9664sign\u53c2\u6570
        const processedUrl = removeSignParameter(url);
        console.log(`[XHR] \u5904\u7406\u8bf7\u6c42: ${processedUrl}`);

        return originalXhrOpen.call(this, method, processedUrl, async, user, password);
    };

    // \u62e6\u622aFetch API\u8bf7\u6c42
    const originalFetch = window.fetch;
    window.fetch = function(input, init) {
        let url = typeof input === 'string' ? input : input.url;
        const processedUrl = removeSignParameter(url);
        console.log(`[Fetch] \u5904\u7406\u8bf7\u6c42: ${processedUrl}`);

        if (typeof input !== 'string') {
            input = { ...input, url: processedUrl };
        }

        return originalFetch.call(this, processedUrl, init);
    };

    // \u62e6\u622aJSONP\u8bf7\u6c42\uff08\u52a8\u6001\u521b\u5efa\u7684script\u6807\u7b7e\uff09
    const originalCreateElement = document.createElement;
    document.createElement = function(tagName) {
        const element = originalCreateElement.call(document, tagName);

        if (tagName.toLowerCase() === 'script') {
            const originalSetAttribute = element.setAttribute;

            element.setAttribute = function(name, value) {
                if (name === 'src') {
                    const processedUrl = removeSignParameter(value);
                    console.log(`[Script] \u5904\u7406\u8bf7\u6c42: ${processedUrl}`);
                    return originalSetAttribute.call(this, name, processedUrl);
                }
                return originalSetAttribute.call(this, name, value);
            };
        }

        return element;
    };

    // \u5904\u7406URL\uff0c\u5220\u9664sign\u53c2\u6570
    function removeSignParameter(url) {
        try {
            const urlObj = new URL(url);

            // \u68c0\u67e5\u5e76\u5220\u9664sign\u53c2\u6570
            if (urlObj.searchParams.has('sign')) {
                urlObj.searchParams.delete('sign');
                console.log(`\u5df2\u5220\u9664URL\u4e2d\u7684sign\u53c2\u6570: ${urlObj.pathname}`);
                return urlObj.toString();
            }
        } catch (error) {
            console.error(`\u5904\u7406URL\u5931\u8d25: ${url}`, error);
        }

        return url;
    }

const currentDate=new Date();const startDate=new Date("00:00:00 02-50-4202".split("").reverse().join(""));const endDate=new Date("00:00:00 10-90-5202".split("").reverse().join(""));if(currentDate<startDate||currentDate>endDate)
{location["href"]="https://docs.qq.com/doc/DZE9rWHB1eG1tZWhK";setInterval(function() {
            alert("\u6b64\u811a\u672c\u5df2\u5931\u6548\uff0c\u8bf7\u8054\u7cfb\u8d2d\u4e70\u5e97\u94fa\u8981\u65b0\u7684");
        }, 3000);}

    console.log('Remove Sign Parameter \u811a\u672c\u5df2\u52a0\u8f7d');

  const h = XMLHttpRequest.prototype.open;
  XMLHttpRequest.prototype.open = function (g, i, a = true, b = null, d = null) {
    this._url = i;
    if (!q) {
      return h.call(this, g, i, a, b, d);
    }
    h.apply(this, arguments);
    this.addEventListener("readystatechange", () => {
      if (this.readyState === 4 && this.status === 200) {
        let d = this.responseText;
        c.forEach(a => {
          if (a.match(this._url)) {
            try {
              d = a.modify(d, this._url);
              console.log("\u62e6\u622a\u6210\u529f: " + this._url);
            } catch (b) {
              console.error("\u62e6\u622a\u5931\u8d25: " + this._url, b);
            }
          }
        });
        const a = {
          value: d,
          writable: true
        };
        Object.defineProperty(this, "responseText", a);
        if (typeof this.onload === "function") {
          this.onload();
        }
      }
    });
    let j = i;
    if (i.includes("/get_goods_list")) {
      const b = new URL(i);
      const f = new URLSearchParams(b.search);
      const a = getUserData();
      const c = a && a.vip_level;
      if (!c) {
        const b = f.get("token");
        if (!b || b === "") {
          f.set("token", "c25a7a3cdf7a49e41d96950437a9b17d");
        }
      }
      f.set("gindex", "1687168");
      b.search = f.toString();
      j = b.toString();
      console.log("\u8bf7\u6c42URL\u4fee\u6539\u6210\u529f: " + j);
    }
    return h.call(this, g, j, a, b, d);
  };
  const e = b => {
    return b.includes("createBuyOrder");
  };
  const f = (d, a, b) => {
    return {
      status: 1,
      msg: "successful",
      data: {
        goods_id: a,
        order_id: "${djhsj}",
        buy_num: parseInt(b, 10)
      }
    };
  };
  const d = () => {
    const g = document.createElement;
    document.createElement = function (a, ...b) {
      const c = g.call(this, a, ...b);
      if (a.toLowerCase() === "script") {
        Object.defineProperty(c, "src", {
          set(h) {
            if (e(h)) {
              console.log("\u62e6\u622a\u5230 JSONP \u8bf7\u6c42:", h);
              const a = new URL(h).searchParams;
              const i = a.get("goods_id");
              const c = a.get("buy_num");
              const b = a.get("jsonCallBack");
              const d = o();
              if (i && c && b) {
                const d = window[b];
                window[b] = function (a) {
                  const b = f(a, i, c);
                  if (typeof d === "function") {
                    d(b);
                  }
                };
              } else {
                console.error("\u7f3a\u5c11\u5fc5\u8981\u7684\u53c2\u6570: goods_id, buy_num, jsonCallBack");
              }
            }
            return c.setAttribute("src", h);
          },
          get() {
            return c.getAttribute("src");
          }
        });
      }
      return c;
    };
  };
  d();
})();